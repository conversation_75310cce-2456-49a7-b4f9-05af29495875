import streamlit as st
from llama_index.embeddings.fastembed import FastEmbedEmbedding
from llama_index.core import Settings
from llama_index.vector_stores.faiss import FaissVectorStore
from llama_index.core import StorageContext, load_index_from_storage
from llama_index.llms.gemini import Gemini
from llama_index.core.prompts import PromptTemplate
from llama_index.core.memory import Chat<PERSON>emoryBuffer
from llama_index.core.llms import ChatMessage
from llama_index.core.storage.chat_store import SimpleChatStore
import os
import json
import uuid
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="Ayurvedic Health Assistant",
    page_icon="🕉️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# ChatGPT-style CSS with delete functionality
st.markdown("""
<style>
    /* Hide Streamlit default elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {display: none;}
    
    /* Remove all default padding and margins */
    .main > div {
        padding: 0 !important;
        margin: 0 !important;
    }

    .block-container {
        padding: 1rem !important;
        margin: 0 !important;
        max-width: none !important;
    }
    
    /* Full height app */
    .stApp {
        height: 100vh;
        overflow: hidden;
    }
    
    /* Sidebar styling - ChatGPT style */
    section[data-testid="stSidebar"] {
        width: 260px !important;
        background: #171717 !important;
        border-right: 1px solid #2d2d2d !important;
        height: 100vh !important;
        overflow: hidden !important;
    }
    
    section[data-testid="stSidebar"] > div {
        padding: 12px !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
    }
    
    /* New Chat Button */
    .new-chat-btn {
        background: transparent !important;
        border: 1px solid #4d4d4f !important;
        color: #ececf1 !important;
        border-radius: 6px !important;
        padding: 12px 16px !important;
        margin-bottom: 16px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        width: 100% !important;
        text-align: left !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }
    
    .new-chat-btn:hover {
        background: #2d2d2d !important;
        border-color: #565869 !important;
    }
    
    /* Chat History */
    .chat-history {
        flex: 1 !important;
        overflow-y: auto !important;
        margin-top: 8px !important;
    }
    
    /* Chat history item container with hover effect for delete button */
    .chat-history-item-container {
        position: relative !important;
        margin-bottom: 4px !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
    }
    
    .chat-history-item-container:hover {
        background: #2d2d2d !important;
    }
    
    .chat-history-item-container:hover .delete-btn {
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    .chat-history-item {
        background: transparent !important;
        border: none !important;
        color: #ececf1 !important;
        padding: 12px 40px 12px 16px !important;
        border-radius: 6px !important;
        font-size: 14px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        width: 100% !important;
        text-align: left !important;
        display: block !important;
        word-wrap: break-word !important;
        line-height: 1.4 !important;
        position: relative !important;
    }
    
    .chat-history-item.active {
        background: #343541 !important;
    }
    
    .chat-history-date {
        font-size: 11px !important;
        color: #8e8ea0 !important;
        margin-top: 4px !important;
    }
    
    /* Delete button */
    .delete-btn {
        position: absolute !important;
        right: 8px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        background: #ef4444 !important;
        border: none !important;
        border-radius: 4px !important;
        color: white !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transition: all 0.2s ease !important;
        font-size: 12px !important;
        z-index: 10 !important;
    }
    
    .delete-btn:hover {
        background: #dc2626 !important;
        transform: translateY(-50%) scale(1.1) !important;
    }
    
    /* Main chat container - Fixed layout like ChatGPT */
    .chat-container {
        height: 100vh !important;
        display: flex !important;
        flex-direction: column !important;
        background: #343541 !important;
        position: relative !important;
    }
    
    /* Chat messages area - Scrollable */
    .messages-container {
        flex: 1 !important;
        overflow-y: auto !important;
        padding: 0 !important;
        background: #343541 !important;
        display: flex !important;
        flex-direction: column !important;
    }
    
    /* Individual message wrapper */
    .message-wrapper {
        width: 100% !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
    
    .message-wrapper.user {
        background: #343541 !important;
    }
    
    .message-wrapper.assistant {
        background: #444654 !important;
    }
    
    /* Message content */
    .message-content {
        max-width: 768px !important;
        margin: 0 auto !important;
        padding: 24px !important;
        display: flex !important;
        gap: 16px !important;
        align-items: flex-start !important;
    }
    
    .message-avatar {
        width: 32px !important;
        height: 32px !important;
        border-radius: 4px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        flex-shrink: 0 !important;
    }
    
    .user-avatar {
        background: #5436da !important;
        color: white !important;
    }
    
    .assistant-avatar {
        background: #19c37d !important;
        color: white !important;
    }
    
    .message-text {
        flex: 1 !important;
        color: #ececf1 !important;
        font-size: 16px !important;
        line-height: 1.6 !important;
        margin: 0 !important;
    }
    
    /* Chat input area - Fixed at bottom */
    .chat-input-container {
        background: #343541 !important;
        padding: 12px 0 24px 0 !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        position: relative !important;
        flex-shrink: 0 !important;
    }
    
    .chat-input-wrapper {
        max-width: 768px !important;
        margin: 0 auto !important;
        padding: 0 24px !important;
        position: relative !important;
    }
    
    /* Style the actual Streamlit chat input */
    .stChatInputContainer {
        background: transparent !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .stChatInputContainer > div {
        background: #40414f !important;
        border: 1px solid #565869 !important;
        border-radius: 12px !important;
        padding: 0 !important;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1) !important;
    }
    
    .stChatInputContainer textarea {
        background: transparent !important;
        color: #ececf1 !important;
        border: none !important;
        font-size: 16px !important;
        padding: 12px 48px 12px 16px !important;
        resize: none !important;
        outline: none !important;
        box-shadow: none !important;
    }
    
    .stChatInputContainer textarea::placeholder {
        color: #8e8ea0 !important;
    }
    
    /* Send button styling */
    .stChatInputContainer button {
        background: #19c37d !important;
        border: none !important;
        border-radius: 6px !important;
        color: white !important;
        padding: 8px !important;
        margin: 6px !important;
        cursor: pointer !important;
        position: absolute !important;
        right: 8px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }
    
    /* Welcome message styling */
    .welcome-container {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 48px 24px !important;
        text-align: center !important;
    }
    
    .welcome-title {
        color: #ececf1 !important;
        font-size: 32px !important;
        font-weight: 600 !important;
        margin-bottom: 16px !important;
    }
    
    .welcome-subtitle {
        color: #8e8ea0 !important;
        font-size: 16px !important;
        margin-bottom: 32px !important;
        max-width: 600px !important;
        line-height: 1.5 !important;
    }
    
    /* Confirmation dialog styling */
    .delete-confirmation {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 1000 !important;
    }
    
    .delete-dialog {
        background: #2d2d2d !important;
        border-radius: 8px !important;
        padding: 24px !important;
        max-width: 400px !important;
        width: 90% !important;
        color: #ececf1 !important;
    }
    
    .delete-dialog h3 {
        margin: 0 0 16px 0 !important;
        color: #ececf1 !important;
    }
    
    .delete-dialog p {
        margin: 0 0 24px 0 !important;
        color: #8e8ea0 !important;
    }
    
    .delete-dialog-buttons {
        display: flex !important;
        gap: 12px !important;
        justify-content: flex-end !important;
    }
    
    .delete-dialog-btn {
        padding: 8px 16px !important;
        border-radius: 6px !important;
        border: none !important;
        cursor: pointer !important;
        font-size: 14px !important;
        font-weight: 500 !important;
    }
    
    .delete-dialog-btn.cancel {
        background: #4d4d4f !important;
        color: #ececf1 !important;
    }
    
    .delete-dialog-btn.confirm {
        background: #ef4444 !important;
        color: white !important;
    }
    
    .delete-dialog-btn:hover {
        opacity: 0.8 !important;
    }
    
    /* Scrollbar styling */
    .messages-container::-webkit-scrollbar,
    .chat-history::-webkit-scrollbar {
        width: 8px !important;
    }
    
    .messages-container::-webkit-scrollbar-track,
    .chat-history::-webkit-scrollbar-track {
        background: transparent !important;
    }
    
    .messages-container::-webkit-scrollbar-thumb,
    .chat-history::-webkit-scrollbar-thumb {
        background: #565869 !important;
        border-radius: 4px !important;
    }
    
    .messages-container::-webkit-scrollbar-thumb:hover,
    .chat-history::-webkit-scrollbar-thumb:hover {
        background: #6b7280 !important;
    }
    
    /* Hide Streamlit chat message containers */
    .stChatMessage {
        display: none !important;
    }
    
    /* Loading animation */
    .typing-indicator {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        color: #8e8ea0 !important;
        font-style: italic !important;
    }
    
    .typing-dots {
        display: flex !important;
        gap: 4px !important;
    }
    
    .typing-dots span {
        width: 6px !important;
        height: 6px !important;
        background: #8e8ea0 !important;
        border-radius: 50% !important;
        animation: typing 1.4s infinite !important;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s !important;
    }
    
    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s !important;
    }
    
    @keyframes typing {
        0%, 60%, 100% {
            opacity: 0.3 !important;
            transform: translateY(0) !important;
        }
        30% {
            opacity: 1 !important;
            transform: translateY(-8px) !important;
        }
    }
</style>
""", unsafe_allow_html=True)

# Memory and chat management functions
def load_chat_store():
    """Load or create chat store for persistent memory"""
    try:
        if os.path.exists("chat_store.json"):
            return SimpleChatStore.from_persist_path("chat_store.json")
        else:
            return SimpleChatStore()
    except:
        return SimpleChatStore()

def save_chat_store(chat_store):
    """Save chat store to disk"""
    try:
        chat_store.persist("chat_store.json")
    except Exception as e:
        st.error(f"Failed to save chat history: {e}")

def load_chat_sessions():
    """Load chat sessions metadata"""
    try:
        if os.path.exists("chat_sessions.json"):
            with open("chat_sessions.json", "r") as f:
                return json.load(f)
        else:
            return {}
    except:
        return {}

def save_chat_sessions(sessions):
    """Save chat sessions metadata"""
    try:
        with open("chat_sessions.json", "w") as f:
            json.dump(sessions, f, indent=2)
    except Exception as e:
        st.error(f"Failed to save sessions: {e}")

def create_new_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return {
        "id": session_id,
        "title": "New Chat",
        "created_at": timestamp,
        "last_updated": timestamp
    }

def update_session_title(sessions, session_id, first_message):
    """Update session title based on first message"""
    if session_id in sessions:
        title = first_message[:30] + "..." if len(first_message) > 30 else first_message
        sessions[session_id]["title"] = title
        sessions[session_id]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def delete_chat_session(session_id):
    """Delete a specific chat session"""
    if session_id in st.session_state.chat_sessions:
        # Remove from sessions
        del st.session_state.chat_sessions[session_id]
        
        # Clear from chat store
        if hasattr(st.session_state.chat_store, '_store') and session_id in st.session_state.chat_store._store:
            del st.session_state.chat_store._store[session_id]
        
        # Save updated data
        save_chat_sessions(st.session_state.chat_sessions)
        save_chat_store(st.session_state.chat_store)
        
        # If we deleted the current session, switch to another one or create new
        if session_id == st.session_state.current_session_id:
            if st.session_state.chat_sessions:
                # Switch to most recent remaining session
                latest_session = max(st.session_state.chat_sessions.items(), key=lambda x: x[1]["last_updated"])
                st.session_state.current_session_id = latest_session[0]
                
                # Load messages for new current session
                st.session_state.memory = ChatMemoryBuffer.from_defaults(
                    token_limit=40000,
                    chat_store=st.session_state.chat_store,
                    chat_store_key=st.session_state.current_session_id
                )
                
                try:
                    chat_history = st.session_state.memory.get()
                    if chat_history:
                        st.session_state.messages = [
                            {"role": msg.role, "content": msg.content} for msg in chat_history
                        ]
                    else:
                        st.session_state.messages = []
                except:
                    st.session_state.messages = []
            else:
                # Create a new session if no sessions left
                new_session = create_new_session()
                st.session_state.chat_sessions[new_session["id"]] = new_session
                st.session_state.current_session_id = new_session["id"]
                
                st.session_state.memory = ChatMemoryBuffer.from_defaults(
                    token_limit=40000,
                    chat_store=st.session_state.chat_store,
                    chat_store_key=new_session["id"]
                )
                
                st.session_state.messages = []
                save_chat_sessions(st.session_state.chat_sessions)

def get_google_api_key():
    api_key = os.environ.get("GOOGLE_API_KEY")
    if not api_key and hasattr(st, "secrets"):
        try:
            api_key = st.secrets.get("GOOGLE_API_KEY")
        except:
            pass
    return api_key

# Initialize session state
if "chat_store" not in st.session_state:
    st.session_state.chat_store = load_chat_store()

if "chat_sessions" not in st.session_state:
    st.session_state.chat_sessions = load_chat_sessions()

if "current_session_id" not in st.session_state:
    if not st.session_state.chat_sessions:
        new_session = create_new_session()
        st.session_state.chat_sessions[new_session["id"]] = new_session
        st.session_state.current_session_id = new_session["id"]
        save_chat_sessions(st.session_state.chat_sessions)
    else:
        latest_session = max(st.session_state.chat_sessions.items(), key=lambda x: x[1]["last_updated"])
        st.session_state.current_session_id = latest_session[0]

if "memory" not in st.session_state:
    st.session_state.memory = ChatMemoryBuffer.from_defaults(
        token_limit=40000,
        chat_store=st.session_state.chat_store,
        chat_store_key=st.session_state.current_session_id
    )

# Initialize delete confirmation state
if "delete_confirmation" not in st.session_state:
    st.session_state.delete_confirmation = None

# Initialize messages
if "messages" not in st.session_state:
    try:
        chat_history = st.session_state.memory.get()
        if chat_history:
            st.session_state.messages = [
                {"role": msg.role, "content": msg.content} for msg in chat_history
            ]
        else:
            st.session_state.messages = []
    except:
        st.session_state.messages = []

# Load query engine and LLM
if "query_engine" not in st.session_state:
    with st.spinner("🕉️ Loading Ayurvedic Knowledge Base..."):
        @st.cache_resource
        def load_embedding_model():
            return FastEmbedEmbedding(
                model_name="BAAI/bge-small-en-v1.5",
                embed_batch_size=32,
                cache_dir="./embedding_cache"
            )
        Settings.embed_model = load_embedding_model()

        vector_store = FaissVectorStore.from_persist_dir("faiss_db")
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            persist_dir="faiss_db"
        )

        index = load_index_from_storage(
            storage_context=storage_context,
            index_id="2a3e044a-5744-41d0-9873-8d679b1571a8"
        )

        api_key = get_google_api_key()
        if not api_key:
            st.error("Please set your GOOGLE_API_KEY in the .env file or as an environment variable")
            st.stop()

        llm = Gemini(model="models/gemini-1.5-pro", api_key=api_key)
        st.session_state.llm = llm

        ayurveda_prompt_str = """You are an expert Ayurvedic physician. 

CRITICAL: Focus ONLY on what the user is currently asking about. If they ask about Apigenin, discuss Apigenin. If they ask about diabetes, discuss diabetes. DO NOT continue previous topics unless explicitly asked.

Context from knowledge base: {context_str}

Current Question: {query_str}

Provide a response following this structure when applicable:

1. Definition/Information
- What is it from an Ayurvedic perspective
- Key properties and characteristics
- Causes: what are the causes for the diseases
- Symptoms: if it's a condition

2. Ayurvedic Treatment/Remedies (if applicable)
- Herbal medicines with specific dosages
- Dietary recommendations
- Lifestyle modifications
- Yoga/pranayama practices
- Panchakarma therapies

3. Benefits/Expected Outcomes
- How it works in the body
- Expected results and timeline
- Scientific backing if available

Guidelines:
- Answer ONLY about the specific topic asked
- Be direct - no greetings or introductions
- Use bullet points for clarity
- Include Sanskrit names with translations
- Provide practical, actionable advice
- If asked about a compound/herb, focus on its properties and uses
- If the topic doesn't fit the structure, adapt accordingly
- End with healthcare consultation reminder for medical conditions

Remember: Each question is independent. Do not reference or continue from previous answers unless the user explicitly asks for elaboration."""
        
        ayurveda_prompt = PromptTemplate(ayurveda_prompt_str)

        st.session_state.query_engine = index.as_query_engine(
            llm=llm,
            similarity_top_k=3,
            text_qa_template=ayurveda_prompt
        )

# Helper functions
def is_greeting_or_chat(query):
    greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening', 
                 'namaste', 'how are you', "what's up", 'greetings', 'thank you', 'thanks',
                 'bye', 'goodbye', 'see you', 'ok', 'okay', 'yes', 'no']
    if not query:
        return False
    query_lower = query.lower().strip()
    for greeting in greetings:
        if query_lower == greeting or query_lower.startswith(greeting):
            return True
    return False

def get_greeting_response(query):
    query_lower = (query or "").lower().strip()
    if any(x in query_lower for x in ['hello', 'hi', 'hey']):
        return "🕉️ Namaste! How can I assist you with your health and wellness today? Feel free to ask me about Ayurvedic remedies, dietary advice, or any health concerns."
    elif 'how are you' in query_lower:
        return "🕉️ I'm here and ready to help you on your wellness journey! How are you feeling today? Any health concern or Ayurvedic topic you'd like to explore?"
    elif 'good morning' in query_lower:
        return "🌅 Good morning! Would you like some Ayurvedic morning routine (Dinacharya) tips or help with any health questions?"
    elif 'good afternoon' in query_lower or 'good evening' in query_lower:
        return "🕉️ Namaste! I hope you're having a peaceful day. How may I assist you with your health and wellness needs?"
    elif 'thank' in query_lower:
        return "🕉️ You're most welcome! Is there anything else about Ayurveda or your health that you'd like to know?"
    elif 'bye' in query_lower or 'goodbye' in query_lower:
        return "🕉️ Namaste! Take care of your health and well-being. Feel free to return anytime you need Ayurvedic guidance."
    else:
        return "🕉️ I'm here to help! Ask me about Ayurvedic medicine, natural remedies, diet, or wellness practices."

def is_follow_up_question(query):
    follow_up_words = ['elaborate', 'more about this', 'explain more', 'tell me more', 
                       'what else', 'continue', 'go on', 'expand on that']
    if not query:
        return False
    query_lower = query.lower()
    return any(phrase in query_lower for phrase in follow_up_words)

# Handle delete confirmation with proper Streamlit approach
if st.session_state.delete_confirmation:
    # Create a modal-like dialog using Streamlit components
    st.markdown("""
    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;">
    </div>
    """, unsafe_allow_html=True)

    # Create the dialog content
    with st.container():
        st.markdown("### ⚠️ Delete Chat?")
        st.write(f"This will permanently delete **{st.session_state.chat_sessions[st.session_state.delete_confirmation]['title']}** and all its messages.")

        col1, col2, col3 = st.columns([1, 1, 1])

        with col1:
            if st.button("Cancel", key="cancel_delete", use_container_width=True):
                st.session_state.delete_confirmation = None
                st.rerun()

        with col3:
            if st.button("Delete", key="confirm_delete", type="primary", use_container_width=True):
                session_to_delete = st.session_state.delete_confirmation
                st.session_state.delete_confirmation = None
                delete_chat_session(session_to_delete)
                st.rerun()

# Sidebar for chat history
with st.sidebar:
    # New chat button
    if st.button("+ New Chat", key="new_chat", help="Start a new conversation"):
        new_session = create_new_session()
        st.session_state.chat_sessions[new_session["id"]] = new_session
        st.session_state.current_session_id = new_session["id"]

        st.session_state.memory = ChatMemoryBuffer.from_defaults(
            token_limit=40000,
            chat_store=st.session_state.chat_store,
            chat_store_key=new_session["id"]
        )

        st.session_state.messages = []
        save_chat_sessions(st.session_state.chat_sessions)
        save_chat_store(st.session_state.chat_store)
        st.rerun()

    # Chat history
    st.markdown('<div style="color: #8e8ea0; font-size: 12px; font-weight: 600; margin: 16px 0 8px 0; text-transform: uppercase; letter-spacing: 0.5px;">Chat History</div>', unsafe_allow_html=True)

    if st.session_state.chat_sessions:
        sorted_sessions = sorted(
            st.session_state.chat_sessions.items(),
            key=lambda x: x[1]["last_updated"],
            reverse=True
        )

        for session_id, session_data in sorted_sessions:
            is_current = session_id == st.session_state.current_session_id
            
            # Create columns for chat item and delete button
            col1, col2 = st.columns([4, 1])
            
            with col1:
                if st.button(
                    session_data['title'],
                    key=f"session_{session_id}",
                    help=f"Switch to: {session_data['title']}",
                    use_container_width=True
                ):
                    st.session_state.current_session_id = session_id

                    st.session_state.memory = ChatMemoryBuffer.from_defaults(
                        token_limit=40000,
                        chat_store=st.session_state.chat_store,
                        chat_store_key=session_id
                    )

                    try:
                        chat_history = st.session_state.memory.get()
                        if chat_history:
                            st.session_state.messages = [
                                {"role": msg.role, "content": msg.content} for msg in chat_history
                            ]
                        else:
                            st.session_state.messages = []
                    except:
                        st.session_state.messages = []

                    st.rerun()
            
            with col2:
                # Only show delete button if there's more than one session
                if len(st.session_state.chat_sessions) > 1:
                    if st.button("🗑️", key=f"delete_{session_id}", help="Delete Chat"):
                        st.session_state.delete_confirmation = session_id

# Main chat interface
if not st.session_state.delete_confirmation:
    # Create main chat container
    st.markdown('<div class="chat-container">', unsafe_allow_html=True)

    # Messages container
    st.markdown('<div class="messages-container">', unsafe_allow_html=True)

    # Display messages or welcome screen
    if st.session_state.messages:
        for message in st.session_state.messages:
            if message["role"] == "user":
                st.markdown(f"""
                <div class="message-wrapper user">
                    <div class="message-content">
                        <div class="message-avatar user-avatar">👤</div>
                        <div class="message-text">{message["content"]}</div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div class="message-wrapper assistant">
                    <div class="message-content">
                        <div class="message-avatar assistant-avatar">🕉️</div>
                        <div class="message-text">{message["content"]}</div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
    else:
        # Welcome screen
        st.markdown("""
        <div class="welcome-container">
            <div class="welcome-title">🕉️ Ayurvedic Health Assistant</div>
            <div class="welcome-subtitle">
                Welcome to your personal Ayurvedic health companion. Ask me about natural remedies,
                dietary advice, lifestyle recommendations, or any health concerns from an Ayurvedic perspective.
            </div>
        </div>
        """, unsafe_allow_html=True)

    st.markdown('</div>', unsafe_allow_html=True)  # Close messages-container

    # Chat input area
    st.markdown('<div class="chat-input-container">', unsafe_allow_html=True)
    st.markdown('<div class="chat-input-wrapper">', unsafe_allow_html=True)

    # Chat input
    if prompt := st.chat_input("Ask about Ayurvedic remedies, diet, or health concerns..."):
        # Add user message
        st.session_state.messages.append({"role": "user", "content": prompt})
        st.session_state.memory.put(ChatMessage(role="user", content=prompt))

        # Update session title if this is the first message
        if len(st.session_state.messages) == 1:
            update_session_title(st.session_state.chat_sessions, st.session_state.current_session_id, prompt)
            save_chat_sessions(st.session_state.chat_sessions)

        # Generate response
        with st.spinner("🕉️ Consulting Ayurvedic wisdom..."):
            try:
                if is_greeting_or_chat(prompt):
                    response = get_greeting_response(prompt)
                elif is_follow_up_question(prompt) and len(st.session_state.messages) > 1:
                    # For follow-up questions, include recent context
                    recent_context = ""
                    if len(st.session_state.messages) >= 3:
                        recent_context = f"Previous question: {st.session_state.messages[-3]['content']}\nPrevious answer: {st.session_state.messages[-2]['content']}\n\n"

                    enhanced_prompt = f"{recent_context}Follow-up question: {prompt}"
                    response = str(st.session_state.query_engine.query(enhanced_prompt))
                else:
                    response = str(st.session_state.query_engine.query(prompt))

                # Add assistant response
                st.session_state.messages.append({"role": "assistant", "content": response})
                st.session_state.memory.put(ChatMessage(role="assistant", content=response))

                # Save chat store
                save_chat_store(st.session_state.chat_store)

            except Exception as e:
                error_msg = f"I apologize, but I encountered an error while processing your question. Please try again. Error: {str(e)}"
                st.session_state.messages.append({"role": "assistant", "content": error_msg})
                st.error(error_msg)

        st.rerun()

    st.markdown('</div>', unsafe_allow_html=True)  # Close chat-input-wrapper
    st.markdown('</div>', unsafe_allow_html=True)  # Close chat-input-container
    st.markdown('</div>', unsafe_allow_html=True)  # Close chat-container
